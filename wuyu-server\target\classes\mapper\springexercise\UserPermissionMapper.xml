<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fiveup.core.permission.mapper.UserPermissionMapper">
    <select id="findByCondition" resultType="com.fiveup.core.permission.entity.UserPermissionInfo">
        SELECT *
        FROM basic_web_user
        WHERE username LIKE CONCAT('%', #{userName}, '%')
        <if test="identity != null">
            AND identity = #{identity}
        </if>
        AND real_name LIKE CONCAT('%', #{realName}, '%')
    </select>
    <delete id="deleteByIds">
        DELETE FROM basic_web_user WHERE id IN
        <foreach collection="ids" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
    </delete>
</mapper>
