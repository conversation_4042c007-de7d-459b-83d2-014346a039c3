<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fiveup.core.notice.mapper.noticeMapper">
    <!-- 插入公告主记录 -->
    <insert id="insertNotice" parameterType="com.fiveup.core.notice.entity.NoticeEntity"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO notice (
            theme,
            content,
            release_time,
            is_important,
            created_by
        ) VALUES (
                     #{theme},
                     #{content},
                     #{releaseTime},
                     #{isImportant},
                     #{createdBy}
                 )
    </insert>

    <!-- 插入公告身份关联记录 -->
    <insert id="insertNoticeIdentityRecord" parameterType="com.fiveup.core.notice.entity.NoticeIdentityEntity">
        INSERT INTO notice_identity_record (
            notice_id,
            identity_id
        ) VALUES (
                     #{noticeId},
                     #{identityId}
                 )
    </insert>

    <!-- 获取所有用户身份信息 -->
    <select id="getIdentityIds" resultType="com.fiveup.core.notice.entity.UserIdentity">
        SELECT
            identity_id AS identityId,
            identity_info AS identityInfo
        FROM basic_user_identity
    </select>
    <select id="selectById" resultType="com.fiveup.core.notice.entity.Notice" parameterType="java.lang.Long">
        SELECT * FROM notice WHERE id = #{noticeId}
    </select>
</mapper>