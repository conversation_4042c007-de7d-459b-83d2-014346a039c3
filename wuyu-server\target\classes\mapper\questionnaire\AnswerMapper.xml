<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.fiveup.core.questionnaire.mapper.AnswerMapper">
    <insert id="addAnswer" parameterType="com.fiveup.core.questionnaire.vo.AnswerVO">
        insert into q_answer(paper_id,question_id,question_type,create_time,answer_content,user_uuid) values(#{paperId},#{questionId},#{questionType},#{createTime},#{answerContent},#{user_uuid})
    </insert>

    <select id="selectByQuestionId" resultType="com.fiveup.core.questionnaire.vo.AnswerVO">
        select * from q_answer where question_id=#{questionId} order by id
    </select>
    <select id="selectUUIDbyPaper" resultType="java.lang.String">
        select distinct user_uuid from  q_answer where paper_id = #{paperId}
    </select>
    <select id="selectAnswersByUUID" resultType="com.fiveup.core.questionnaire.vo.AnswerVO">
        select * from q_answer where user_uuid=#{UUID}
    </select>
    <select id="selectOption" resultType="java.lang.String">
        select content from q_options where question_id=#{question_id} and sequence=#{sequence}
     </select>


<!--    <resultMap id="AnswerVO" type="com.example.hotel.vo.AnswerVO">-->
<!--        <result column="id" property="id"></result>-->
<!--        <result column="question_id" property="questionId"></result>-->
<!--        <result column="paper_id" property="paperId"></result>-->
<!--        <result column="question_type" property="questionType"></result>-->
<!--        <result column="create_time" property="createTime"></result>-->
<!--        <result column="answer_content" property="answerContent"></result>-->
<!--    </resultMap>-->
</mapper>
