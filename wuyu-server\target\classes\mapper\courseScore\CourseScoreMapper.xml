<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fiveup.core.courseScore.mapper.CourseScoreMapper">
    <select id="findByCondition" resultType="com.fiveup.core.courseScore.entity.CourseScore">
        SELECT *
        FROM course_score
        WHERE course_name LIKE CONCAT('%', #{courseName}, '%')
        <if test="courseType != null">
            AND course_type = #{courseType}
        </if>
        AND teacher_name LIKE CONCAT('%', #{teacherName}, '%')
        AND student_num LIKE CONCAT('%', #{studentNum}, '%')
        AND student_name LIKE CONCAT('%', #{studentName}, '%')
    </select>
    <delete id="deleteByIds">
        DELETE FROM course_score WHERE id IN
        <foreach collection="ids" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
    </delete>
</mapper>