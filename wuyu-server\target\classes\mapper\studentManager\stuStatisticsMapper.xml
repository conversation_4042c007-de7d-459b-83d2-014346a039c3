<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fiveup.core.studentManager.mapper.StatisticsMapper">

    <!-- 基础查询条件（修正参数引用） -->
    <sql id="baseFilter">
        <where>
            <!-- 去掉所有 "query." 前缀 -->
            <if test="studentNum != null and studentNum != ''">
                bs.student_num like concat('%', #{studentNum}, '%')
            </if>
            <if test="studentName != null and studentName != ''">
                and bs.student_name like concat('%', #{studentName}, '%')
            </if>
            <if test="gender != null">
                and bs.gender = #{gender}
            </if>
            <if test="schoolId != null">
                and bs.school_id = #{schoolId}
            </if>
            <if test="gradeId != null">
                and bs.grade_id = #{gradeId}
            </if>
            <if test="className != null and className != ''">
                and bc.class_name like concat('%', #{className}, '%')
            </if>
            and bs.deleted = 0
        </where>
    </sql>

    <!-- 统计性别比例 -->
    <select id="countGenderRatio" resultType="map">
        SELECT
            SUM(CASE WHEN bs.gender = 1 THEN 1 ELSE 0 END) AS maleCount,
            SUM(CASE WHEN bs.gender = 0 THEN 1 ELSE 0 END) AS femaleCount
        FROM basic_student bs
                 LEFT JOIN basic_class bc ON bs.class_id = bc.id
    </select>

    <!-- 统计年级比例 -->
    <select id="countGradeRatio" resultType="map">
        SELECT
            bs.grade_id AS gradeId,
            COUNT(*) AS count
        FROM basic_student bs
            LEFT JOIN basic_class bc ON bs.class_id = bc.id
        GROUP BY bs.grade_id
    </select>

    <!-- 统计学校比例 -->
    <select id="countSchoolRatio" resultType="map">
        SELECT
            bs.school_id AS schoolId,
            COUNT(*) AS count
        FROM basic_student bs
            LEFT JOIN basic_class bc ON bs.class_id = bc.id
        GROUP BY bs.school_id
    </select>

    <!-- 统计筛选后的性别比例 -->
    <select id="countFilteredGenderRatio" resultType="map">
        SELECT
        SUM(CASE WHEN bs.gender = 1 THEN 1 ELSE 0 END) AS maleCount,
        SUM(CASE WHEN bs.gender = 0 THEN 1 ELSE 0 END) AS femaleCount
        FROM basic_student bs
        LEFT JOIN basic_class bc ON bs.class_id = bc.id
        <include refid="baseFilter"/>
    </select>

    <!-- 统计筛选后的年级比例 -->
    <select id="countFilteredGradeRatio" resultType="map">
        SELECT
        bs.grade_id AS gradeId,
        COUNT(*) AS count
        FROM basic_student bs
        LEFT JOIN basic_class bc ON bs.class_id = bc.id
        <include refid="baseFilter"/>
        GROUP BY bs.grade_id
    </select>

    <!-- 统计筛选后的学校比例 -->
    <select id="countFilteredSchoolRatio" resultType="map">
        SELECT
        bs.school_id AS schoolId,
        COUNT(*) AS count
        FROM basic_student bs
        LEFT JOIN basic_class bc ON bs.class_id = bc.id
        <include refid="baseFilter"/>
        GROUP BY bs.school_id
    </select>
</mapper>