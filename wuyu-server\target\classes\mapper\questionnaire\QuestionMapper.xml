<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fiveup.core.questionnaire.mapper.QuestionMapper">
    <insert id="addQuestion" parameterType="com.fiveup.core.questionnaire.po.Question" useGeneratedKeys="true" keyProperty="id">
        insert into q_question(paper_id) values(#{paperId})
    </insert>

    <update id="updateQuestion" parameterType="com.fiveup.core.questionnaire.po.Question">
        update q_question set type=#{type},title=#{title} where id=#{id}
    </update>
    <select id="selectByQuestionId" resultType="com.fiveup.core.questionnaire.po.Question">
        select * from q_question where id=#{id}
    </select>

    <delete id="deleteQuestion">
        delete from q_question where id=#{questionId}
    </delete>
    <delete id="deleteByPaperId">
        delete from q_question where paper_id=#{paperId}
    </delete>

    <select id="selectByPaperId" resultType="com.fiveup.core.questionnaire.po.Question">
        select * from q_question where paper_id=#{paperId} order by id
    </select>


<!--    <resultMap id="Question" type="com.example.hotel.po.Question">-->
<!--        <result column="id" property="id"></result>-->
<!--        <result column="paper_id" property="paperId"></result>-->
<!--        <result column="type" property="type"></result>-->
<!--        <result column="title" property="title"></result>-->
<!--    </resultMap>-->
</mapper>
