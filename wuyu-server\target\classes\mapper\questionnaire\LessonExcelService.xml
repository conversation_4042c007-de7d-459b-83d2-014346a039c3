<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fiveup.core.questionnaire.mapper.PaperMapper">
    <insert id="addPaper" parameterType="com.fiveup.core.questionnaire.vo.PaperVO" useGeneratedKeys="true" keyProperty="id">
        insert into q_paper(user_id,title,description,start_time,end_time,paper_status) values(#{userId},#{title},#{description},#{startTime},#{endTime},#{status})
    </insert>
    <update id="updatePaper" parameterType="com.fiveup.core.questionnaire.vo.PaperVO">
        update q_paper set title=#{title},description=#{description},start_time=#{startTime},end_time=#{endTime},status=#{status} where id=#{id}
    </update>
    <delete id="deletePaper">
        delete from q_paper where paper_id=#{paperId}
    </delete>
    <select id="selectByPaperId" resultType="com.fiveup.core.questionnaire.vo.PaperVO">
        select * from q_paper where paper_id=#{paperId}
    </select>
    <select id="getUserPapers" resultType="com.fiveup.core.questionnaire.vo.PaperVO">
        select * from q_paper where user_id=#{userId}
    </select>

    <select id="getTimePapers" resultType="com.fiveup.core.questionnaire.vo.PaperVO">
        select * from q_paper where start_time is not null
    </select>
    <select id="selectTimeOut" resultType="com.fiveup.core.questionnaire.po.Paper">
        select * from q_paper where  unix_timestamp(NOW())>unix_timestamp(end_time) and paper_status!='2'
    </select>

    <update id="changeStatus">
        update q_paper set paper_status=#{paperStatus} where paper_id =#{id} and paper_status!='2'
    </update>

<!--    <resultMap id="PaperVO" type="com.example.hotel.vo.PaperVO">-->
<!--        <result column="id" property="id"></result>-->
<!--        <result column="user_id" property="userId"></result>-->
<!--        <result column="title" property="title"></result>-->
<!--        <result column="description" property="description"></result>-->
<!--        <result column="start_time" property="startTime"></result>-->
<!--        <result column="end_time" property="endTime"></result>-->
<!--        <result column="status" property="status" typeHandler="org.apache.ibatis.type.EnumOrdinalTypeHandler"></result>-->
<!--    </resultMap>-->
</mapper>
