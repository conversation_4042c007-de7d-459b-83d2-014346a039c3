<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fiveup.core.cultivation.mapper.MinutesMapper">
    <select id="getOne"
            resultType="com.fiveup.core.cultivation.entity.Minutes"
            parameterType="integer">
        select cmn.*, btc.teacher_name
        from ctv_minutes cmn
        left join basic_teacher btc on btc.id = cmn.tid
        where cmn.is_deleted = false and cmn.id = #{id}
    </select>
    <select id="getAll"
            resultType="com.fiveup.core.cultivation.entity.Minutes"
            parameterType="com.fiveup.core.cultivation.entity.Minutes">
        select cmn.*, btc.teacher_name
        from ctv_minutes cmn
        left join basic_teacher btc on btc.id = cmn.tid
        where cmn.is_deleted = false
            <if test="tid != null">
               and cmn.tid = #{tid}
            </if>
            <if test="mid != null">
                and cmn.mid = #{mid}
            </if>
    </select>
</mapper>
