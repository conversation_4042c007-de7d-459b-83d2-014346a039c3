<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fiveup.core.collection.mapper.ColViewMapper">

    <select id="selectAllView" resultType="com.fiveup.core.collection.model.ColView">
        select DISTINCT sms_student.id,sms_student.name,sms_student.s_number,sms_student.class_id,ex_exam_record.exam_join_score,education.ThemeType,education.Theme,education.ThemeContent,education.TeacherRemark,education.ParentRemark,`comment`.ParentScore,`comment`.TeacherScore
        from sms_student,ex_exam_record,education,`comment`
        WHERE sms_student.id = ex_exam_record.s_id and sms_student.id= education.StudentId and education.ID = `comment`.Id
    </select>
    <insert id="insertView" parameterType="com.fiveup.core.collection.model.ColView" useGeneratedKeys="true" keyProperty="id">
        INSERT into q_view(id,name,s_number,class_id,exam_join_score,ThemeType,Theme,ThemeContent,TeacherRemark,ParentRemark,ParentScore,TeacherScore)
        select sms_student.id,sms_student.name,sms_student.s_number,sms_student.class_id,ex_exam_record.exam_join_score,education.ThemeType,education.Theme,education.ThemeContent,education.TeacherRemark,education.ParentRemark,`comment`.ParentScore,`comment`.TeacherScore
        from sms_student,ex_exam_record,education,`comment`
        WHERE sms_student.id = ex_exam_record.s_id and sms_student.id= education.StudentId and education.ID = `comment`.Id
    </insert>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fiveup.core.collection.model.ColView">
        <id column="id" property="id" />
        <result column="s_number" property="sNumber" />
        <result column="name" property="name" />
        <result column="class_id" property="classId" />
        <result column="Theme" property="theme" />
        <result column="TeacherRemark" property="teacherremark" />
        <result column="ParentRemark" property="parentremark" />
        <result column="TeacherScore" property="teacherscore" />
        <result column="ParentScore" property="parentscore" />
        <result column="ThemeType" property="themetype" />
        <result column="ThemeContent" property="themecontent" />
        <result column="exam_join_score" property="examJoinScore" />
    </resultMap>

</mapper>
