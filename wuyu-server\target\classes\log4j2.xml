<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="info">

    <Properties>
        <Property name="log-path">/home/<USER>/log</Property>
        <Property name="biz-log-file">app.log</Property>
        <Property name="error-log-file">error.log</Property>
    </Properties>

    <Appenders>

        <Console name="console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
        </Console>

        <!-- 业务日志 -->
        <RollingRandomAccessFile name="biz-log-appender" fileName="${log-path}/${biz-log-file}" immediateFlush="true"
                                 filePattern="${log-path}/${biz-log-file}.%d{yyyyMMddHH}">
            <PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="3" modulate="true"/>
            </Policies>
        </RollingRandomAccessFile>

        <!-- 错误日志 -->
        <RollingRandomAccessFile name="err-log-appender" fileName="${log-path}/${error-log-file}" immediateFlush="true"
                                 filePattern="${log-path}/${error-log-file}.%d{yyyyMMdd}">
            <PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <Filters>
                <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </RollingRandomAccessFile>

    </Appenders>

    <Loggers>

        <Root level="info" includeLocation="true">
            <AppenderRef ref="console"/>
            <AppenderRef ref="biz-log-appender"/>
            <AppenderRef ref="err-log-appender"/>
        </Root>
    </Loggers>

</Configuration>