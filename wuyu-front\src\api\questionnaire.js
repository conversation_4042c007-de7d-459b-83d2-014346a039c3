// import axios from 'axios'

// export function fetchAllQuestionnaireList(userId) {
//   return axios({
//    /* url: '/api/paper/'+userId+'/getUserPapers',*/
//     url: '/api/paper/'+1+'/getUserPapers',
//     method: 'GET',
//
//   })
// }
// export function fetchAllQuestionnaireList(userId) {
//   return request({
//     url: '/api/paper/'+ userId+'/getUserPapers',
//     method: 'GET',
//     params: userId
//   })
// }
