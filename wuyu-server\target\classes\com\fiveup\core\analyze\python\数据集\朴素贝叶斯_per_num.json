[{"de优秀": 538, "zhi优秀": 321, "ti优秀": 519, "mei优秀": 107, "lao优秀": 378, "mei良好": 108, "mei一般": 123, "ti良好": 17, "ti一般": 5, "lao良好": 90, "mei不及格": 204, "zhi良好": 142, "zhi一般": 61, "zhi不及格": 18, "lao一般": 50, "ti不及格": 1, "lao不及格": 24, "de良好": 3, "de不及格": 1}, {"de优秀": 517, "zhi优秀": 84, "ti优秀": 460, "mei不及格": 448, "lao良好": 110, "lao优秀": 126, "zhi不及格": 114, "zhi良好": 151, "lao一般": 151, "lao不及格": 139, "zhi一般": 177, "ti不及格": 9, "mei优秀": 2, "mei一般": 62, "ti良好": 40, "ti一般": 17, "de良好": 7, "mei良好": 14, "de一般": 2}, {"de优秀": 576, "zhi良好": 105, "ti优秀": 438, "mei不及格": 574, "lao良好": 58, "lao不及格": 382, "lao优秀": 34, "ti良好": 83, "zhi一般": 164, "zhi不及格": 301, "zhi优秀": 40, "lao一般": 136, "mei优秀": 1, "mei一般": 26, "ti一般": 52, "ti不及格": 37, "de良好": 29, "mei良好": 9, "de一般": 4, "de不及格": 1}, {"de优秀": 782, "zhi不及格": 969, "ti优秀": 329, "mei不及格": 1074, "lao一般": 37, "lao不及格": 1030, "de良好": 128, "ti良好": 168, "ti不及格": 457, "zhi一般": 86, "zhi优秀": 9, "de一般": 38, "mei一般": 10, "lao良好": 17, "zhi良好": 26, "ti一般": 136, "lao优秀": 6, "mei优秀": 3, "mei良好": 3, "de不及格": 142}]