server:
  port: 9080 # master分支端口，合并到master注意别改这
spring:
  application:
    name: fiveup-core
  jackson:
    default-property-inclusion: non_null # springboot默认解析对象为json对象时属性值为null时不予显示此属性
  datasource:
    url: jdbc:mysql://***********:3306/fiveup?useUnicode=true&characterEncoding=utf-8&serverTimezone=Asia/Shanghai&serverTimezone=UTC&useSSL=false&allowPublicKeyRetrieval=true
    username: Wuyu_Developer
    password: D3G@qrukHR5S
    driver: com.mysql.jdbc.Driver
    # 连接池配置
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  servlet:
    multipart:
      max-file-size: 128MB #上传文件总大值
      max-request-size: 10MB  #单个文件的最大值
      # application.yml示例

#management:
#  health:
#    redis:
#      enabled: false  # 禁用Redis健康检查


mybatis-plus:
  typeAliasesPackage: com.fiveup.core.**.**.domain
  mapper-locations: classpath:/mapper/**/*.xml
  global-config:
    db-config:
      id-type: auto
  configuration:
    auto-mapping-behavior: partial
    map-underscore-to-camel-case: true # 开启驼峰原则，将数据库带下划线的字段自动转为驼峰原则的命名
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #mybatis-plus配置控制台打印完整带参数SQL语句




schedule:
  auto-copy:
    enabled: true  # 定时任务默认开启
